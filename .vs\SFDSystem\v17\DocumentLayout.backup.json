{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\sys\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\services\\professionaltemplatesservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:services\\professionaltemplatesservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\viewmodels\\reportviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:viewmodels\\reportviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\views\\professionalmessageswindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:views\\professionalmessageswindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\viewmodels\\professionalmessagesviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:viewmodels\\professionalmessagesviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\services\\databaseseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:services\\databaseseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\models\\contracttemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:models\\contracttemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|c:\\users\\<USER>\\desktop\\sys\\views\\addofficerswindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{27326FF1-7897-4073-A59C-C84C0750677F}|SFDSystem.csproj|solutionrelative:views\\addofficerswindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 419, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ProfessionalMessagesWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\ProfessionalMessagesWindow.xaml.cs", "RelativeDocumentMoniker": "Views\\ProfessionalMessagesWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\ProfessionalMessagesWindow.xaml.cs", "RelativeToolTip": "Views\\ProfessionalMessagesWindow.xaml.cs", "ViewState": "AgIAAHQBAAAAAAAAAAApwIEBAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T19:06:14.822Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ReportViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\ViewModels\\ReportViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\ReportViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\ViewModels\\ReportViewModel.cs", "RelativeToolTip": "ViewModels\\ReportViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:59:57.5Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ProfessionalTemplatesService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Services\\ProfessionalTemplatesService.cs", "RelativeDocumentMoniker": "Services\\ProfessionalTemplatesService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Services\\ProfessionalTemplatesService.cs", "RelativeToolTip": "Services\\ProfessionalTemplatesService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAG4AAABaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:59:53.089Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProfessionalMessagesViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\ViewModels\\ProfessionalMessagesViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\ProfessionalMessagesViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\ViewModels\\ProfessionalMessagesViewModel.cs", "RelativeToolTip": "ViewModels\\ProfessionalMessagesViewModel.cs", "ViewState": "AgIAAFcCAAAAAAAAAAApwGQCAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:57:38.99Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DatabaseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Services\\DatabaseService.cs", "RelativeDocumentMoniker": "Services\\DatabaseService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Services\\DatabaseService.cs", "RelativeToolTip": "Services\\DatabaseService.cs", "ViewState": "AgIAAMQEAAAAAAAAAAAlwNAEAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:25:10.327Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "DatabaseSeeder.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Services\\DatabaseSeeder.cs", "RelativeDocumentMoniker": "Services\\DatabaseSeeder.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Services\\DatabaseSeeder.cs", "RelativeToolTip": "Services\\DatabaseSeeder.cs", "ViewState": "AgIAAM8AAAAAAAAAAAAlwNwAAABqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:24:29.46Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "ContractTemplate.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Models\\ContractTemplate.cs", "RelativeDocumentMoniker": "Models\\ContractTemplate.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Models\\ContractTemplate.cs", "RelativeToolTip": "Models\\ContractTemplate.cs", "ViewState": "AgIAAAgAAAAAAAAAAAA4wA4AAAABAQAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:24:26.561Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Data\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Data\\ApplicationDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Data\\ApplicationDbContext.cs", "RelativeToolTip": "Data\\ApplicationDbContext.cs", "ViewState": "AgIAANoBAAAAAAAAAAAAANoBAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:19:03.27Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "AddOfficersWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\AddOfficersWindow.xaml.cs", "RelativeDocumentMoniker": "Views\\AddOfficersWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\sys\\Views\\AddOfficersWindow.xaml.cs", "RelativeToolTip": "Views\\AddOfficersWindow.xaml.cs", "ViewState": "AgIAADwAAAAAAAAAAAAYwAwAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T18:18:38.81Z"}]}, {"DockedHeight": 150, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{b1e99781-ab81-11d0-b683-00aa00a3ee26}"}]}]}]}