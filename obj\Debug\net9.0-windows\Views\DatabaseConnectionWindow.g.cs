﻿#pragma checksum "..\..\..\..\Views\DatabaseConnectionWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C3040106A9AF30579B24249821C9000F5B8E6656"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// DatabaseConnectionWindow
    /// </summary>
    public partial class DatabaseConnectionWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 62 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ServerDatabaseRadio;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LocalDatabaseRadio;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LocalDatabaseInfo;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ServerSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ServerNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton WindowsAuthRadio;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton SqlAuthRadio;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LocalDatabasePanel;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocalDatabaseNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocalDatabasePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SqlAuthPanel;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestConnectionButton;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateDatabaseButton;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/databaseconnectionwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ServerDatabaseRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 63 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
            this.ServerDatabaseRadio.Checked += new System.Windows.RoutedEventHandler(this.ServerDatabaseRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LocalDatabaseRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 65 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
            this.LocalDatabaseRadio.Checked += new System.Windows.RoutedEventHandler(this.LocalDatabaseRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 3:
            this.LocalDatabaseInfo = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.ServerSettingsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.ServerNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.DatabaseNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.WindowsAuthRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 8:
            this.SqlAuthRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 9:
            this.LocalDatabasePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.LocalDatabaseNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.LocalDatabasePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.SqlAuthPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.UsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 15:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TestConnectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
            this.TestConnectionButton.Click += new System.Windows.RoutedEventHandler(this.TestConnectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CreateDatabaseButton = ((System.Windows.Controls.Button)(target));
            
            #line 133 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
            this.CreateDatabaseButton.Click += new System.Windows.RoutedEventHandler(this.CreateDatabaseButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 139 "..\..\..\..\Views\DatabaseConnectionWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

