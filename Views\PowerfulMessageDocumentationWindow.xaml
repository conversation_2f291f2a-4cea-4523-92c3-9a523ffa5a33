<Window x:Class="DriverManagementSystem.Views.PowerfulMessageDocumentationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:DriverManagementSystem.Converters"
        Title="🚀 توثيق الرسائل النصية - النظام القوي"
        Height="1000" Width="1600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        WindowState="Maximized"
        Background="#F5F7FA">

    <Window.Resources>
        <local:FileNameConverter x:Key="FileNameConverter"/>
        
        <!-- Styles -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PowerButton" TargetType="Button">
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>

            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#CCCCCC" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.4"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E1E5E9"/>

            <Setter Property="Background" Value="White"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#667eea" Padding="30">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🚀" FontSize="40" Foreground="White" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <StackPanel>
                        <TextBlock Text="توثيق الرسائل النصية"
                                 FontSize="24" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="{Binding Documentation.VisitNumber, StringFormat='رقم الزيارة: {0}'}"
                                 FontSize="16" Foreground="#E8F4FD" Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Live Statistics -->
                <Border Grid.Column="1" Background="#80FFFFFF" Padding="20">
                    <StackPanel Orientation="Horizontal">
                        <StackPanel Margin="0,0,20,0" HorizontalAlignment="Center">
                            <TextBlock Text="{Binding ImagePathsCount}" FontSize="28" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Text="صور" FontSize="12" Foreground="#E8F4FD" HorizontalAlignment="Center"/>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="{Binding Documentation.DocumentationDate, StringFormat=dd/MM}" FontSize="28" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Text="التاريخ" FontSize="12" Foreground="#E8F4FD" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="400"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Panel - Form -->
                <StackPanel Grid.Column="0">
                    <!-- Visit Info Card -->
                    <Border Style="{StaticResource ModernCard}">
                        <StackPanel>
                            <TextBlock Text="📋 معلومات الزيارة" FontSize="18" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,20"/>
                            
                            <TextBlock Text="رقم الزيارة:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Documentation.VisitNumber}" IsReadOnly="True"
                                   Style="{StaticResource ModernTextBox}" Background="#F8F9FA" Margin="0,0,0,15"/>

                        </StackPanel>
                    </Border>

                    <!-- Visit Conductors Card -->
                    <Border Style="{StaticResource ModernCard}">
                        <StackPanel>
                            <TextBlock Text="👥 القائمين بالزيارة" FontSize="18" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,20"/>

                            <!-- Dynamic Visit Conductors -->
                            <ItemsControl ItemsSource="{Binding VisitConductors}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Margin="0,0,0,15">
                                            <TextBlock Text="{Binding Label}" FontWeight="Bold" Margin="0,0,0,5"/>
                                            <TextBox Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                   Style="{StaticResource ModernTextBox}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>





                    <!-- Images Status Card -->
                    <Border Style="{StaticResource ModernCard}">
                        <StackPanel>
                            <TextBlock Text="📊 حالة الصور" FontSize="18" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,15"/>

                            <TextBlock Text="{Binding ImagesFolderPath, StringFormat='المجلد: {0}'}"
                                     FontSize="11"
                                     Foreground="#6C757D"
                                     TextWrapping="Wrap"
                                     Margin="0,0,0,10"/>

                            <!-- Progress Bar for Images -->
                            <Grid Margin="0,0,0,10">
                                <ProgressBar Value="{Binding ImagePathsCount}" Maximum="4" Height="8"
                                           Background="#E9ECEF" Foreground="#28A745"/>
                                <TextBlock Text="{Binding ImagePathsCount, StringFormat=\{0\}/4 صور}"
                                         HorizontalAlignment="Center" VerticalAlignment="Center"
                                         FontSize="10" FontWeight="Bold"/>
                            </Grid>
                        </StackPanel>
                    </Border>


                </StackPanel>

                <!-- Right Panel - Split Preview -->
                <Border Grid.Column="1" Style="{StaticResource ModernCard}" Margin="10,10,20,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <TextBlock Grid.Row="0" Text="🖼️ معاينة الصور" FontSize="20" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,15"/>

                        <!-- Split Preview Area -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="5"/>
                                <ColumnDefinition Width="2*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Thumbnails Section (Right Side) -->
                            <Border Grid.Column="0" Background="White" BorderBrush="#DEE2E6" BorderThickness="1">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <!-- Thumbnails Header -->
                                    <TextBlock Grid.Row="0" Text="📋 قائمة الصور" FontSize="14" FontWeight="Bold"
                                             Foreground="#495057" Margin="10,10,10,5"/>

                                    <!-- Thumbnails List -->
                                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                        <ItemsControl ItemsSource="{Binding ImagePaths}" Margin="5">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border BorderBrush="#DEE2E6" BorderThickness="1" Margin="0,0,0,5"
                                                          Background="White" Padding="3" Cursor="Hand"
                                                          MouseLeftButtonDown="SideThumbnail_MouseLeftButtonDown">
                                                        <Grid>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                            </Grid.RowDefinitions>

                                                            <Image Grid.Row="0" Source="{Binding}" Height="60"
                                                                 Stretch="UniformToFill" Margin="2"/>

                                                            <TextBlock Grid.Row="1" Text="{Binding Converter={StaticResource FileNameConverter}}"
                                                                     TextAlignment="Center" Margin="2"
                                                                     TextTrimming="CharacterEllipsis" FontSize="10"/>

                                                            <Button Grid.Row="2" Content="🗑️"
                                                                  Command="{Binding DataContext.RemoveImageCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                                  CommandParameter="{Binding}"
                                                                  Background="#DC3545" Foreground="White"
                                                                  BorderThickness="0" Padding="5,2" FontSize="8"
                                                                  Margin="2"/>
                                                        </Grid>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </ScrollViewer>

                                    <!-- Empty State for Thumbnails -->
                                    <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <StackPanel.Style>
                                            <Style TargetType="StackPanel">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding ImagePathsCount}" Value="0">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </StackPanel.Style>
                                        <TextBlock Text="📷" FontSize="40" Foreground="#DEE2E6" HorizontalAlignment="Center"/>
                                        <TextBlock Text="لا توجد صور" FontSize="12" Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Splitter -->
                            <GridSplitter Grid.Column="1" Width="5" Background="#DEE2E6" HorizontalAlignment="Stretch"/>

                            <!-- Large Preview Section (Left Side) -->
                            <Border Grid.Column="2" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" Width="716" Height="578" ScrollViewer.VerticalScrollBarVisibility="Auto">
                                <Grid>
                                    <!-- Large Preview Image -->
                                    <Border x:Name="LargePreviewBorder" Visibility="Collapsed">
                                        <StackPanel>
                                            <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" MaxHeight="500">
                                                <Image x:Name="LargePreviewImage" Stretch="Uniform" Margin="10"/>
                                            </ScrollViewer>
                                            <TextBlock x:Name="LargePreviewName" Text="اسم الصورة"
                                                     Background="#343A40" Foreground="White"
                                                     Padding="10,5" TextAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Empty State for Large Preview -->
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <StackPanel.Style>
                                            <Style TargetType="StackPanel">
                                                <Setter Property="Visibility" Value="Visible"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding ImagePathsCount}" Value="0">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </StackPanel.Style>
                                        <TextBlock Text="📷" FontSize="80" Foreground="#DEE2E6" HorizontalAlignment="Center"/>
                                        <TextBlock Text="اختر صورة للمعاينة" FontSize="18" Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                        <TextBlock Text="انقر على صورة من القائمة الجانبية" FontSize="14" Foreground="#ADB5BD" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                </Border>
            </Grid>
        </ScrollViewer>

        <!-- Footer Actions -->
        <Border Grid.Row="2" Background="#343A40" Padding="25">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="💾 حفظ التوثيق"
                      Command="{Binding SaveDocumentationCommand}"
                      Background="#28A745"
                      Foreground="White"
                      Style="{StaticResource PowerButton}"
                      Margin="0,0,15,0"/>

                <Button Content="📁 اختيار مجلد صور"
                      Command="{Binding SelectImagesFolderCommand}"
                      Background="#17A2B8"
                      Foreground="White"
                      Style="{StaticResource PowerButton}"
                      Margin="0,0,10,0"/>

                <Button Content="🖼️ إضافة صورة"
                      Command="{Binding AddSingleImageCommand}"
                      Background="#6F42C1"
                      Foreground="White"
                      Style="{StaticResource PowerButton}"
                      Margin="0,0,20,0"/>

                <Button Content="❌ إغلاق"
                      Click="CloseButton_Click"
                      Background="#DC3545"
                      Foreground="White"
                      Style="{StaticResource PowerButton}"/>
            </StackPanel>
        </Border>

        <!-- Image Preview Overlay -->
        <Grid x:Name="ImagePreviewOverlay" Grid.RowSpan="3" Background="#80000000" Visibility="Collapsed">
        <Border Background="White"
                MaxWidth="900"
                MaxHeight="700"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="10" BlurRadius="20" Opacity="0.5"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <TextBlock Text="🖼️ معاينة الصورة"
                             FontSize="20"
                             FontWeight="Bold"
                             Foreground="#2C3E50"
                             HorizontalAlignment="Left"/>
                    <Button Content="❌"
                          Click="CloseImagePreview_Click"
                          Background="#DC3545"
                          Foreground="White"
                          BorderThickness="0"
                          Padding="10,5"
                          HorizontalAlignment="Right"
                          Cursor="Hand"/>
                </Grid>

                <!-- Image -->
                <ScrollViewer Grid.Row="1"
                            HorizontalScrollBarVisibility="Auto"
                            VerticalScrollBarVisibility="Auto"
                            MaxHeight="500">
                    <Image x:Name="PreviewImage"
                         Stretch="Uniform"
                         HorizontalAlignment="Center"
                         VerticalAlignment="Center"/>
                </ScrollViewer>

                <!-- Footer -->
                <TextBlock x:Name="PreviewImageName"
                         Grid.Row="2"
                         Text="اسم الصورة"
                         FontSize="14"
                         Foreground="#6C757D"
                         HorizontalAlignment="Center"
                         Margin="0,15,0,0"/>
            </Grid>
        </Border>
        </Grid>
    </Grid>
</Window>
