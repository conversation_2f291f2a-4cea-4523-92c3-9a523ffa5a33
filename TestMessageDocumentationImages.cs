using System;
using System.Collections.Generic;
using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.Views;

namespace DriverManagementSystem
{
    /// <summary>
    /// فئة اختبار لنظام توثيق الرسائل النصية الذكي
    /// يختبر التخطيط التكيفي للصور حسب العدد
    /// </summary>
    public class TestMessageDocumentationImages
    {
        /// <summary>
        /// اختبار النظام مع صورة واحدة
        /// </summary>
        public static void TestSingleImage()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-001",
                    VisitNumber = "زيارة-001"
                };

                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg"
                };

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الصورة الواحدة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار النظام مع صورتين
        /// </summary>
        public static void TestTwoImages()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-002",
                    VisitNumber = "زيارة-002"
                };

                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image2.jpg"
                };

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الصورتين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار النظام مع ثلاث صور
        /// </summary>
        public static void TestThreeImages()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-003",
                    VisitNumber = "زيارة-003"
                };

                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image2.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image3.jpg"
                };

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الثلاث صور: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار النظام مع أربع صور
        /// </summary>
        public static void TestFourImages()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-004",
                    VisitNumber = "زيارة-004"
                };

                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image2.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image3.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image4.jpg"
                };

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الأربع صور: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار النظام بدون صور
        /// </summary>
        public static void TestNoImages()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-000",
                    VisitNumber = "زيارة-000"
                };

                var imagePaths = new List<string>();

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار عدم وجود صور: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار شامل لجميع السيناريوهات
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                MessageBox.Show("سيتم اختبار جميع تخطيطات الصور\nاضغط موافق للبدء", "اختبار شامل",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                // اختبار بدون صور
                MessageBox.Show("اختبار: بدون صور", "اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                TestNoImages();

                // اختبار صورة واحدة
                MessageBox.Show("اختبار: صورة واحدة", "اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                TestSingleImage();

                // اختبار صورتين
                MessageBox.Show("اختبار: صورتان", "اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                TestTwoImages();

                // اختبار ثلاث صور
                MessageBox.Show("اختبار: ثلاث صور", "اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                TestThreeImages();

                // اختبار أربع صور
                MessageBox.Show("اختبار: أربع صور", "اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                TestFourImages();

                MessageBox.Show("تم الانتهاء من جميع الاختبارات بنجاح!", "اكتمل",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاختبار الشامل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار مع صور وهمية للعرض التوضيحي
        /// </summary>
        public static void TestWithDemoImages()
        {
            try
            {
                // إنشاء مسارات وهمية للاختبار
                var demoImagePaths = new List<string>
                {
                    "demo_image_1.jpg",
                    "demo_image_2.jpg",
                    "demo_image_3.jpg"
                };

                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-تجريبي",
                    VisitNumber = "زيارة-تجريبية"
                };

                var window = new MessageDocumentationImagesReportWindow(documentation, demoImagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الصور التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
