<Window x:Class="DriverManagementSystem.Views.MessageDocumentationImagesReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="توثيق الرسائل النصية للنزول الميداني - الصور المرفقة" 
        Height="800" Width="600"
        WindowStartupLocation="CenterScreen"
        Background="White"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="White" BorderBrush="Black" BorderThickness="2" Margin="10,10,10,0">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Side - Report Numbers -->
                <StackPanel Grid.Row="0" Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Top">
                    <TextBlock Text="{Binding ReportNumber, StringFormat='رقم التقرير: {0}'}" 
                             FontSize="12" FontWeight="Bold" Margin="0,2"/>
                    <TextBlock Text="{Binding VisitNumber, StringFormat='رقم الزيارة: {0}'}" 
                             FontSize="12" FontWeight="Bold" Margin="0,2"/>
                </StackPanel>

                <!-- Center - Logo and Title -->
                <StackPanel Grid.Row="0" Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Top">
                    <Image Source="/logos/sfd.png" Height="60" Width="60" Margin="0,0,0,5"/>
                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="14" FontWeight="Bold" 
                             HorizontalAlignment="Center" Margin="0,2"/>
                    <TextBlock Text="فرع محافظة ذمار" FontSize="12" FontWeight="Bold" 
                             HorizontalAlignment="Center" Margin="0,2"/>
                </StackPanel>

                <!-- Right Side - Organization Info -->
                <StackPanel Grid.Row="0" Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Top">
                    <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" 
                             HorizontalAlignment="Right" Margin="0,2"/>
                    <TextBlock Text="وزارة التخطيط والتعاون الدولي" FontSize="10" 
                             HorizontalAlignment="Right" Margin="0,2"/>
                </StackPanel>

                <!-- Main Title -->
                <TextBlock Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" 
                         Text="توثيق الرسائل النصية للنزول الميداني" 
                         FontSize="18" FontWeight="Bold" 
                         HorizontalAlignment="Center" 
                         Margin="0,15,0,10"/>

                <!-- Subtitle -->
                <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3" 
                         Text="الصور المرفقة والوثائق" 
                         FontSize="14" FontWeight="Bold" 
                         HorizontalAlignment="Center" 
                         Margin="0,5,0,10"/>
            </Grid>
        </Border>

        <!-- Main Content - Smart Images Grid -->
        <Border Grid.Row="1" Background="White" BorderBrush="Black" BorderThickness="2" Margin="10,5,10,0">
            <Grid x:Name="ImagesContainer" Margin="15">

                <!-- Single Image Layout -->
                <Grid x:Name="SingleImageGrid" Visibility="Collapsed">
                    <Border BorderBrush="Black" BorderThickness="1" Margin="50,20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image1" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="10"/>
                            <TextBlock x:Name="Image1Label" Grid.Row="1" Text="الصورة الأولى"
                                     FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="8,4"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState1">
                                <TextBlock Text="📷" FontSize="60" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="16" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>

                <!-- Two Images Layout -->
                <Grid x:Name="TwoImagesGrid" Visibility="Collapsed">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Image 1 -->
                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image2_1" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="8"/>
                            <TextBlock x:Name="Image2_1Label" Grid.Row="1" Text="الصورة الأولى"
                                     FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="6,3"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState2_1">
                                <TextBlock Text="📷" FontSize="40" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="12" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Image 2 -->
                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image2_2" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="8"/>
                            <TextBlock x:Name="Image2_2Label" Grid.Row="1" Text="الصورة الثانية"
                                     FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="6,3"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState2_2">
                                <TextBlock Text="📷" FontSize="40" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="12" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>

                <!-- Three Images Layout -->
                <Grid x:Name="ThreeImagesGrid" Visibility="Collapsed">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Image 1 (Top Left) -->
                    <Border Grid.Row="0" Grid.Column="0" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image3_1" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="6"/>
                            <TextBlock x:Name="Image3_1Label" Grid.Row="1" Text="الصورة الأولى"
                                     FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="5,2"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState3_1">
                                <TextBlock Text="📷" FontSize="30" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="10" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Image 2 (Top Right) -->
                    <Border Grid.Row="0" Grid.Column="1" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image3_2" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="6"/>
                            <TextBlock x:Name="Image3_2Label" Grid.Row="1" Text="الصورة الثانية"
                                     FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="5,2"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState3_2">
                                <TextBlock Text="📷" FontSize="30" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="10" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Image 3 (Bottom Center) -->
                    <Border Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" BorderBrush="Black" BorderThickness="1" Margin="80,5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image3_3" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="6"/>
                            <TextBlock x:Name="Image3_3Label" Grid.Row="1" Text="الصورة الثالثة"
                                     FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="5,2"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState3_3">
                                <TextBlock Text="📷" FontSize="30" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="10" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>

                <!-- Four Images Layout -->
                <Grid x:Name="FourImagesGrid" Visibility="Collapsed">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Image 1 -->
                    <Border Grid.Row="0" Grid.Column="0" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image4_1" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="6"/>
                            <TextBlock x:Name="Image4_1Label" Grid.Row="1" Text="الصورة الأولى"
                                     FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="5,2"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState4_1">
                                <TextBlock Text="📷" FontSize="30" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="10" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Image 2 -->
                    <Border Grid.Row="0" Grid.Column="1" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image4_2" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="6"/>
                            <TextBlock x:Name="Image4_2Label" Grid.Row="1" Text="الصورة الثانية"
                                     FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="5,2"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState4_2">
                                <TextBlock Text="📷" FontSize="30" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="10" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Image 3 -->
                    <Border Grid.Row="1" Grid.Column="0" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image4_3" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="6"/>
                            <TextBlock x:Name="Image4_3Label" Grid.Row="1" Text="الصورة الثالثة"
                                     FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="5,2"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState4_3">
                                <TextBlock Text="📷" FontSize="30" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="10" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Image 4 -->
                    <Border Grid.Row="1" Grid.Column="1" BorderBrush="Black" BorderThickness="1" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Image x:Name="Image4_4" Grid.Row="0" Stretch="Fill"
                                   HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="6"/>
                            <TextBlock x:Name="Image4_4Label" Grid.Row="1" Text="الصورة الرابعة"
                                     FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"
                                     Background="LightGray" Padding="5,2"/>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      x:Name="EmptyState4_4">
                                <TextBlock Text="📷" FontSize="30" Foreground="LightGray" HorizontalAlignment="Center"/>
                                <TextBlock Text="لا توجد صورة" FontSize="10" Foreground="Gray" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" Background="White" BorderBrush="Black" BorderThickness="2" Margin="10,5,10,10">
            <StackPanel Margin="10">
                <TextBlock Text="صفحة (2) من (2)" FontSize="12" FontWeight="Bold" 
                         HorizontalAlignment="Center" Margin="0,5"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
