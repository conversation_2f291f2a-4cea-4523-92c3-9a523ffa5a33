using System;
using System.Collections.Generic;
using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.Views;

namespace DriverManagementSystem
{
    /// <summary>
    /// اختبار صفحة الإقرار الجديدة
    /// </summary>
    public static class TestAcknowledgmentWindow
    {
        public static void ShowTestWindow()
        {
            try
            {
                // إنشاء زيارة تجريبية
                var testVisit = CreateTestVisit();
                
                // فتح نافذة الإقرار
                var acknowledgmentWindow = new AcknowledgmentWindow(testVisit);
                acknowledgmentWindow.ShowDialog();
                
                Console.WriteLine("✅ تم اختبار صفحة الإقرار بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار صفحة الإقرار: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                Console.WriteLine($"❌ خطأ في اختبار صفحة الإقرار: {ex.Message}");
            }
        }

        private static FieldVisit CreateTestVisit()
        {
            var visit = new FieldVisit
            {
                Id = 1,
                VisitNumber = "2025-001",
                MissionPurpose = "زيارة ميدانية لمتابعة تنفيذ مشروع تطوير المجتمعات الريفية في منطقة ذمار",
                DepartureDate = DateTime.Now.AddDays(1),
                ReturnDate = DateTime.Now.AddDays(3),
                DaysCount = 3,
                ApprovalBy = "مدير الفرع",
                SelectedDrivers = "السائق: أحمد محمد علي\nالهاتف: 777123456\nنوع السيارة: تويوتا هايلكس",
                Visitors = new List<FieldVisitor>
                {
                    new FieldVisitor
                    {
                        Id = 1,
                        Name = "محمد أحمد الديلمي",
                        OfficerName = "محمد أحمد الديلمي",
                        OfficerRank = "مدير الفرع",
                        OfficerCode = "EMP001",
                        PhoneNumber = "777111222"
                    },
                    new FieldVisitor
                    {
                        Id = 2,
                        Name = "علي حسن المحطوري",
                        OfficerName = "علي حسن المحطوري",
                        OfficerRank = "ضابط مشروع",
                        OfficerCode = "EMP002",
                        PhoneNumber = "777333444"
                    },
                    new FieldVisitor
                    {
                        Id = 3,
                        Name = "فاطمة عبدالله الزهراني",
                        OfficerName = "فاطمة عبدالله الزهراني",
                        OfficerRank = "مختصة اجتماعية",
                        OfficerCode = "EMP003",
                        PhoneNumber = "777555666"
                    }
                }
            };

            return visit;
        }
    }
}
