using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Media.Imaging;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة عرض الصور المرفقة في توثيق الرسائل النصية
    /// </summary>
    public partial class MessageDocumentationImagesReportWindow : Window
    {
        private MessageDocumentation _documentation;
        private List<string> _imagePaths;

        public MessageDocumentationImagesReportWindow(MessageDocumentation documentation)
        {
            InitializeComponent();
            _documentation = documentation;
            LoadDocumentationData();
            LoadImages();
        }

        public MessageDocumentationImagesReportWindow(MessageDocumentation documentation, List<string> imagePaths)
        {
            InitializeComponent();
            _documentation = documentation;
            _imagePaths = imagePaths;
            LoadDocumentationData();
            LoadCustomImages();
        }

        /// <summary>
        /// تحميل بيانات التوثيق الأساسية
        /// </summary>
        private void LoadDocumentationData()
        {
            try
            {
                DataContext = new
                {
                    ReportNumber = _documentation?.ReportNumber ?? "غير محدد",
                    VisitNumber = _documentation?.VisitNumber ?? "غير محدد"
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات التوثيق: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الصور المحفوظة
        /// </summary>
        private void LoadImages()
        {
            try
            {
                // جمع مسارات الصور الموجودة فقط (تجاهل المسارات الفارغة أو غير الموجودة)
                var imagePaths = new List<string>();

                // فحص كل مسار صورة والتأكد من وجوده قبل الإضافة
                var allImagePaths = new[]
                {
                    _documentation?.ImagePath1,
                    _documentation?.ImagePath2,
                    _documentation?.ImagePath3,
                    _documentation?.ImagePath4
                };

                foreach (var path in allImagePaths)
                {
                    if (!string.IsNullOrEmpty(path) && File.Exists(path))
                    {
                        imagePaths.Add(path);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"🖼️ تم العثور على {imagePaths.Count} صورة صالحة من أصل {allImagePaths.Length} مسارات");

                // تحميل الصور بالتخطيط الذكي
                LoadImagesWithSmartLayout(imagePaths);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصور: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل صورة واحدة (دالة قديمة للتوافق)
        /// </summary>
        private void LoadImage(string imagePath, System.Windows.Controls.Image imageControl,
            System.Windows.Controls.TextBlock labelControl, System.Windows.Controls.StackPanel emptyStateControl,
            string defaultLabel)
        {
            // استخدام الدالة المحسنة الجديدة
            LoadOptimizedImage(imagePath, imageControl, labelControl, emptyStateControl, defaultLabel);
        }

        /// <summary>
        /// طباعة الصفحة
        /// </summary>
        public void PrintPage()
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // تحديد حجم الطباعة
                    var pageSize = new Size(printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight);
                    
                    // تحضير النافذة للطباعة
                    this.Measure(pageSize);
                    this.Arrange(new Rect(pageSize));
                    
                    // طباعة النافذة
                    printDialog.PrintVisual(this, "توثيق الرسائل النصية - الصور المرفقة");
                    
                    MessageBox.Show("تم إرسال الصفحة للطباعة بنجاح", "طباعة", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ الصفحة كصورة
        /// </summary>
        public void SaveAsImage(string filePath)
        {
            try
            {
                // تحديد حجم الصفحة (A4 بدقة 300 DPI)
                var pageSize = new Size(2480, 3508);
                
                // تحضير النافذة للحفظ
                this.Measure(pageSize);
                this.Arrange(new Rect(pageSize));
                
                // إنشاء RenderTargetBitmap
                var renderBitmap = new RenderTargetBitmap(
                    (int)pageSize.Width, (int)pageSize.Height, 300, 300, System.Windows.Media.PixelFormats.Pbgra32);
                
                renderBitmap.Render(this);
                
                // حفظ الصورة
                var encoder = new System.Windows.Media.Imaging.PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));
                
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    encoder.Save(fileStream);
                }
                
                MessageBox.Show($"تم حفظ الصفحة بنجاح في:\n{filePath}", "حفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصفحة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل الصور المخصصة من قائمة المسارات
        /// </summary>
        private void LoadCustomImages()
        {
            try
            {
                if (_imagePaths != null && _imagePaths.Count > 0)
                {
                    // تصفية المسارات الموجودة فقط
                    var validImagePaths = _imagePaths.Where(path => !string.IsNullOrEmpty(path) && File.Exists(path)).ToList();

                    System.Diagnostics.Debug.WriteLine($"🖼️ تم تمرير {_imagePaths.Count} مسار، منها {validImagePaths.Count} صالح");

                    // تحميل الصور بالتخطيط الذكي
                    LoadImagesWithSmartLayout(validImagePaths);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مسارات صور مخصصة");
                    LoadImagesWithSmartLayout(new List<string>());
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الصور المخصصة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الصور بتخطيط ذكي حسب العدد
        /// </summary>
        private void LoadImagesWithSmartLayout(List<string> imagePaths)
        {
            try
            {
                // إخفاء جميع التخطيطات أولاً
                SingleImageGrid.Visibility = Visibility.Collapsed;
                TwoImagesGrid.Visibility = Visibility.Collapsed;
                ThreeImagesGrid.Visibility = Visibility.Collapsed;
                FourImagesGrid.Visibility = Visibility.Collapsed;

                int imageCount = imagePaths?.Count ?? 0;
                System.Diagnostics.Debug.WriteLine($"🎯 تحميل التخطيط الذكي: {imageCount} صورة");

                switch (imageCount)
                {
                    case 0:
                        // لا توجد صور - إظهار رسالة فارغة
                        System.Diagnostics.Debug.WriteLine("📭 عرض حالة فارغة - لا توجد صور");
                        ShowEmptyState();
                        break;
                    case 1:
                        // صورة واحدة
                        System.Diagnostics.Debug.WriteLine("🖼️ عرض تخطيط صورة واحدة");
                        SingleImageGrid.Visibility = Visibility.Visible;
                        LoadSingleImage(imagePaths[0]);
                        break;
                    case 2:
                        // صورتان
                        System.Diagnostics.Debug.WriteLine("🖼️🖼️ عرض تخطيط صورتين");
                        TwoImagesGrid.Visibility = Visibility.Visible;
                        LoadTwoImages(imagePaths);
                        break;
                    case 3:
                        // ثلاث صور
                        System.Diagnostics.Debug.WriteLine("🖼️🖼️🖼️ عرض تخطيط ثلاث صور");
                        ThreeImagesGrid.Visibility = Visibility.Visible;
                        LoadThreeImages(imagePaths);
                        break;
                    case 4:
                    default:
                        // أربع صور أو أكثر
                        System.Diagnostics.Debug.WriteLine($"🖼️🖼️🖼️🖼️ عرض تخطيط أربع صور (العدد الفعلي: {imageCount})");
                        FourImagesGrid.Visibility = Visibility.Visible;
                        LoadFourImages(imagePaths);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الصور بالتخطيط الذكي: {ex.Message}");
            }
        }

        /// <summary>
        /// إظهار حالة فارغة عند عدم وجود صور
        /// </summary>
        private void ShowEmptyState()
        {
            SingleImageGrid.Visibility = Visibility.Visible;
            EmptyState1.Visibility = Visibility.Visible;
            Image1.Visibility = Visibility.Collapsed;
            Image1Label.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// تحميل صورة واحدة
        /// </summary>
        private void LoadSingleImage(string imagePath)
        {
            LoadOptimizedImage(imagePath, Image1, Image1Label, EmptyState1, "الصورة الوحيدة");
        }

        /// <summary>
        /// تحميل صورتين
        /// </summary>
        private void LoadTwoImages(List<string> imagePaths)
        {
            LoadOptimizedImage(imagePaths[0], Image2_1, Image2_1Label, EmptyState2_1, "الصورة الأولى");
            LoadOptimizedImage(imagePaths[1], Image2_2, Image2_2Label, EmptyState2_2, "الصورة الثانية");
        }

        /// <summary>
        /// تحميل ثلاث صور
        /// </summary>
        private void LoadThreeImages(List<string> imagePaths)
        {
            LoadOptimizedImage(imagePaths[0], Image3_1, Image3_1Label, EmptyState3_1, "الصورة الأولى");
            LoadOptimizedImage(imagePaths[1], Image3_2, Image3_2Label, EmptyState3_2, "الصورة الثانية");
            LoadOptimizedImage(imagePaths[2], Image3_3, Image3_3Label, EmptyState3_3, "الصورة الثالثة");
        }

        /// <summary>
        /// تحميل أربع صور
        /// </summary>
        private void LoadFourImages(List<string> imagePaths)
        {
            LoadOptimizedImage(imagePaths[0], Image4_1, Image4_1Label, EmptyState4_1, "الصورة الأولى");
            LoadOptimizedImage(imagePaths[1], Image4_2, Image4_2Label, EmptyState4_2, "الصورة الثانية");
            LoadOptimizedImage(imagePaths[2], Image4_3, Image4_3Label, EmptyState4_3, "الصورة الثالثة");

            // التحقق من وجود الصورة الرابعة
            if (imagePaths.Count > 3)
            {
                LoadOptimizedImage(imagePaths[3], Image4_4, Image4_4Label, EmptyState4_4, "الصورة الرابعة");
            }
            else
            {
                // إخفاء الصورة الرابعة إذا لم تكن موجودة
                Image4_4.Visibility = Visibility.Collapsed;
                Image4_4Label.Visibility = Visibility.Collapsed;
                EmptyState4_4.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// تحميل صورة محسنة بحجم مناسب للإطار
        /// </summary>
        private void LoadOptimizedImage(string imagePath, System.Windows.Controls.Image imageControl,
            System.Windows.Controls.TextBlock labelControl, System.Windows.Controls.StackPanel emptyStateControl,
            string defaultLabel)
        {
            try
            {
                if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                {
                    // تحميل الصورة بحجم محسن ومناسب للإطار
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;

                    // تحديد حد أقصى للحجم لتحسين الأداء وضمان ملائمة الإطار
                    bitmap.DecodePixelWidth = 400;  // عرض أقصى 400 بكسل (مخفض من 600)
                    bitmap.DecodePixelHeight = 300; // ارتفاع أقصى 300 بكسل (مخفض من 450)

                    bitmap.EndInit();

                    imageControl.Source = bitmap;
                    imageControl.Visibility = Visibility.Visible;

                    // ضبط خصائص العرض لضمان عدم التكبير
                    imageControl.Stretch = System.Windows.Media.Stretch.Uniform;
                    imageControl.StretchDirection = System.Windows.Controls.StretchDirection.DownOnly;
                    imageControl.HorizontalAlignment = System.Windows.HorizontalAlignment.Center;
                    imageControl.VerticalAlignment = System.Windows.VerticalAlignment.Center;

                    // تحديث التسمية بناءً على اسم الملف
                    var fileName = Path.GetFileNameWithoutExtension(imagePath);
                    labelControl.Text = !string.IsNullOrEmpty(fileName) ? fileName : defaultLabel;
                    labelControl.Visibility = Visibility.Visible;

                    // إخفاء حالة الفراغ
                    emptyStateControl.Visibility = Visibility.Collapsed;

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل الصورة بنجاح: {fileName} - الحجم: 400x300");
                }
                else
                {
                    // إظهار حالة الفراغ
                    imageControl.Visibility = Visibility.Collapsed;
                    labelControl.Visibility = Visibility.Collapsed;
                    emptyStateControl.Visibility = Visibility.Visible;

                    System.Diagnostics.Debug.WriteLine($"⚠️ الصورة غير موجودة: {imagePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الصورة {imagePath}: {ex.Message}");

                // إظهار حالة الفراغ في حالة الخطأ
                imageControl.Visibility = Visibility.Collapsed;
                labelControl.Visibility = Visibility.Collapsed;
                emptyStateControl.Visibility = Visibility.Visible;
            }
        }
    }
}
