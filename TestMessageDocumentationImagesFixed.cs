using System;
using System.Collections.Generic;
using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.Views;

namespace DriverManagementSystem
{
    /// <summary>
    /// اختبار النظام المحسن لتوثيق الرسائل النصية
    /// يختبر التخطيط التكيفي المحسن للصور حسب العدد
    /// </summary>
    public class TestMessageDocumentationImagesFixed
    {
        /// <summary>
        /// اختبار النظام مع ثلاث صور (يجب أن يعرض 3 إطارات فقط)
        /// </summary>
        public static void TestThreeImagesFixed()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-محسن-003",
                    VisitNumber = "زيارة-محسنة-003"
                };

                // ثلاث صور فقط - يجب أن يعرض 3 إطارات
                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image2.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image3.jpg"
                };

                MessageBox.Show($"🧪 اختبار النظام المحسن\n\n" +
                              $"📊 عدد الصور: {imagePaths.Count}\n" +
                              $"🎯 التخطيط المتوقع: ثلاث صور (2 في الأعلى + 1 في الأسفل)\n" +
                              $"✅ يجب ألا تظهر إطارات فارغة\n\n" +
                              $"اضغط موافق لفتح النافذة", 
                              "اختبار النظام المحسن", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الثلاث صور المحسن: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار النظام مع صورتين (يجب أن يعرض إطارين فقط)
        /// </summary>
        public static void TestTwoImagesFixed()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-محسن-002",
                    VisitNumber = "زيارة-محسنة-002"
                };

                // صورتان فقط - يجب أن يعرض إطارين
                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg",
                    @"C:\Users\<USER>\Desktop\sys\test_images\image2.jpg"
                };

                MessageBox.Show($"🧪 اختبار النظام المحسن\n\n" +
                              $"📊 عدد الصور: {imagePaths.Count}\n" +
                              $"🎯 التخطيط المتوقع: صورتان جنباً إلى جنب\n" +
                              $"✅ يجب ألا تظهر إطارات فارغة\n\n" +
                              $"اضغط موافق لفتح النافذة", 
                              "اختبار النظام المحسن", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الصورتين المحسن: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار النظام مع صورة واحدة (يجب أن يعرض إطار واحد كبير)
        /// </summary>
        public static void TestSingleImageFixed()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-محسن-001",
                    VisitNumber = "زيارة-محسنة-001"
                };

                // صورة واحدة فقط - يجب أن يعرض إطار واحد كبير
                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg"
                };

                MessageBox.Show($"🧪 اختبار النظام المحسن\n\n" +
                              $"📊 عدد الصور: {imagePaths.Count}\n" +
                              $"🎯 التخطيط المتوقع: صورة واحدة كبيرة في المنتصف\n" +
                              $"✅ يجب ألا تظهر إطارات فارغة\n\n" +
                              $"اضغط موافق لفتح النافذة", 
                              "اختبار النظام المحسن", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الصورة الواحدة المحسن: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار شامل لجميع السيناريوهات المحسنة
        /// </summary>
        public static void RunAllFixedTests()
        {
            try
            {
                MessageBox.Show("🚀 سيتم اختبار النظام المحسن لتوثيق الرسائل النصية\n\n" +
                              "✅ التحسينات الجديدة:\n" +
                              "• عدد الإطارات = عدد الصور بالضبط\n" +
                              "• لا توجد إطارات فارغة\n" +
                              "• تخطيط ذكي ومتكيف\n" +
                              "• تسجيل مفصل للتشخيص\n\n" +
                              "اضغط موافق للبدء", 
                              "اختبار شامل محسن",
                              MessageBoxButton.OK, MessageBoxImage.Information);

                // اختبار صورة واحدة
                TestSingleImageFixed();

                // اختبار صورتين
                TestTwoImagesFixed();

                // اختبار ثلاث صور
                TestThreeImagesFixed();

                MessageBox.Show("✅ تم الانتهاء من جميع الاختبارات المحسنة!\n\n" +
                              "تحقق من نافذة Output في Visual Studio لرؤية التسجيل المفصل", 
                              "اكتمال الاختبارات", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل الاختبارات المحسنة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار مع مسارات وهمية (لاختبار التعامل مع الملفات غير الموجودة)
        /// </summary>
        public static void TestWithMissingFiles()
        {
            try
            {
                var documentation = new MessageDocumentation
                {
                    ReportNumber = "تقرير-اختبار-ملفات-مفقودة",
                    VisitNumber = "زيارة-اختبار-مفقودة"
                };

                // مسارات وهمية - بعضها موجود وبعضها مفقود
                var imagePaths = new List<string>
                {
                    @"C:\Users\<USER>\Desktop\sys\test_images\image1.jpg", // قد يكون موجود
                    @"C:\fake\path\missing1.jpg", // مفقود
                    @"C:\Users\<USER>\Desktop\sys\test_images\image2.jpg", // قد يكون موجود
                    @"C:\fake\path\missing2.jpg", // مفقود
                    @"C:\Users\<USER>\Desktop\sys\test_images\image3.jpg"  // قد يكون موجود
                };

                MessageBox.Show($"🧪 اختبار التعامل مع الملفات المفقودة\n\n" +
                              $"📊 عدد المسارات المرسلة: {imagePaths.Count}\n" +
                              $"🎯 النظام سيتجاهل الملفات غير الموجودة\n" +
                              $"✅ سيعرض فقط الصور الموجودة\n\n" +
                              $"تحقق من نافذة Output لرؤية التفاصيل\n\n" +
                              $"اضغط موافق لفتح النافذة", 
                              "اختبار الملفات المفقودة", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                var window = new MessageDocumentationImagesReportWindow(documentation, imagePaths);
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الملفات المفقودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
